// ignore_for_file: unnecessary_string_interpolations

import 'package:bibl/models/league_user_model.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/profile_controller.dart';
import 'controllers/leaderboard_controller.dart';
import 'widgets/customappbar.dart';
import 'widgets/leaderboard_widgets.dart';
import 'widgets/league_clock_widget.dart';

class Leaderboard extends StatefulWidget {
  const Leaderboard({super.key});

  @override
  State<Leaderboard> createState() => _LeaderboardState();
}

class _LeaderboardState extends State<Leaderboard> {
  final ProfileController profileController = Get.find();
  final LeaderboardController leaderboardController = Get.find();
  Future<void> _addUserToLeague(
      String league, String groupId, String userId, String username) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final groupRef = firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(groupId);
      final playerDocRef = groupRef.collection('players').doc(userId);

      await firestore.runTransaction((transaction) async {
        // Step 1: Reads sequentially
        final groupDoc = await transaction.get(groupRef);
        final playerDoc = await transaction.get(playerDocRef);

        // Step 2: Writes sequentially
        if (!groupDoc.exists) {
          transaction.set(groupRef, {
            'createdAt': Timestamp.now(),
            'league': league,
          });
        }

        if (!playerDoc.exists) {
          transaction.set(
            playerDocRef,
            LeaguePlayerModel(
              username: username.isNotEmpty
                  ? username
                  : profileController.userr.value.uniqueName ?? 'Unknown',
              league: league,
              score: 0,
              playerId: userId,
              lastUpdated: Timestamp.now(),
            ).toJson(),
          );
        }
      });

      // Update users collection (outside transaction, as it's a separate write)
      await firestore.collection('users').doc(userId).update({
        'league': league,
        'groupId': groupId,
        'lastUpdated': Timestamp.now(),
      });
      profileController.userr.value.league = league;
      profileController.userr.value.groupId = groupId;
      profileController.userr.refresh();
    } catch (e) {
      debugPrint('Error adding user to league: $e');
      Get.snackbar('Error', 'Failed to assign user to league: $e');
    }
  }

  Future<void> _assignUserToBronzana(String userId, String username) async {
    try {
      final firestore = FirebaseFirestore.instance;
      const league = 'Bronzana';

      // Find an available group or create a new one
      final groupsSnapshot = await firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .get();

      String groupId = '';
      bool groupFound = false;

      // Check for an existing group with less than 10 players
      for (var groupDoc in groupsSnapshot.docs) {
        final playersSnapshot =
            await groupDoc.reference.collection('players').get();
        if (playersSnapshot.docs.length < 10) {
          groupId = groupDoc.id;
          groupFound = true;
          break;
        }
      }

      // If no group found, create a new one
      if (!groupFound) {
        final groupCount = groupsSnapshot.docs.length + 1;
        groupId = 'group_$groupCount';
      }

      // Add user to league and group
      await _addUserToLeague(league, groupId, userId, username);
    } catch (e) {
      debugPrint('Error assigning user to Bronzana: $e');
      Get.snackbar('Error', 'Failed to assign to Bronzana League: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Please log in to view the leaderboard.')),
      );
    }

    final userID = user.uid;

    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          isBackButton: false,
          widget: null,
          title: 'umniLab lige',
        ),
        body: StreamBuilder<DocumentSnapshot>(
          stream: FirebaseFirestore.instance
              .collection('users')
              .doc(userID)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                  child: CircularProgressIndicator(color: mainColor));
            }

            if (snapshot.hasError) {
              return const Center(child: Text('Error loading user data.'));
            }

            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(child: Text('User not found.'));
            }

            final userData = snapshot.data!.data() as Map<String, dynamic>;
            String league = userData['league'] ?? '';
            String groupId = userData['groupId'] ?? '';

            // If league or groupId is empty, assign to Bronzana
            if (league.isEmpty || groupId.isEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                await _assignUserToBronzana(
                    userID, profileController.userr.value.uniqueName ?? '');
              });
              return const Center(
                  child: CircularProgressIndicator(color: mainColor));
            }

            return Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Column(
                          children: [
                            const SizedBox(height: 16),
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Txt(
                                  txt:
                                      'Click this button to check what happens after the week ends',
                                  maxLines: 2,
                                  fontSize: 16),
                            ),
                            ElevatedButton(
                              onPressed: () async {
                                try {
                                  await FirebaseFunctions.instance
                                      .httpsCallable('manualUpdateLeaderboard')
                                      .call();
                                } catch (e) {
                                  Get.snackbar('Error',
                                      'Failed to update leaderboard: $e');
                                }
                              },
                              child: const Text('Update Leaderboard'),
                            ),
                            Expanded(
                              child: StreamBuilder<QuerySnapshot>(
                                stream: FirebaseFirestore.instance
                                    .collection('leaderboards')
                                    .doc(league)
                                    .collection('groups')
                                    .doc(groupId)
                                    .collection('players')
                                    .orderBy('score', descending: true)
                                    .orderBy('lastUpdated', descending: false)
                                    .snapshots(),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState ==
                                          ConnectionState.waiting ||
                                      snapshot.hasError) {
                                    return const Center(
                                      child: CircularProgressIndicator(
                                        color: mainColor,
                                      ),
                                    );
                                  }

                                  final players = snapshot.data!.docs
                                      .map((doc) =>
                                          LeaguePlayerModel.fromSnap(doc))
                                      .toList();

                                  final currentUserId =
                                      profileController.userr.value.uid ?? '';
                                  final isUserInLeague = players.any((player) =>
                                      player.playerId == currentUserId);

                                  if (!isUserInLeague &&
                                      currentUserId.isNotEmpty) {
                                    _addUserToLeague(
                                        league,
                                        groupId,
                                        currentUserId,
                                        profileController
                                                .userr.value.uniqueName ??
                                            '');
                                  }

                                  if (players.isEmpty) {
                                    return const Center(
                                        child: Text(
                                            'No players found in this group.'));
                                  }

                                  return ListView(
                                    padding: const EdgeInsets.all(16.0),
                                    children: [
                                      leaderboardListItem(players),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(child: leagueClockWidget()),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget leaderboardListItem(List<LeaguePlayerModel> players) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const RankHeaderWidget(
          isOnResult: false,
        ),
        const SizedBox(height: 16),
        const Text(
          'Tabela',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.08),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: DataTable(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade200, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              headingRowColor: WidgetStateProperty.all(Colors.grey.shade50),
              dividerThickness: 0,
              horizontalMargin: 20,
              columnSpacing: 40,
              headingRowHeight: 50,
              dataRowMinHeight: 56,
              dataRowMaxHeight: 56,
              columns: [
                DataColumn(
                  label: Text(
                    'Rank',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Ime',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Neuroni',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
              rows: players.asMap().entries.map((entry) {
                final index = entry.key;
                final player = entry.value;
                final currentUid = profileController.userr.value.uid ?? '';
                final currentName =
                    profileController.userr.value.uniqueName ?? '';
                final isCurrentUser = player.playerId == currentUid;
                final textColor = isCurrentUser
                    ? const Color(0xff7CE099)
                    : Colors.grey.shade800;
                final bgColor = isCurrentUser
                    ? const Color(0xff7CE099).withValues(alpha: 0.08)
                    : (index % 2 == 0 ? Colors.white : Colors.grey.shade50);

                return DataRow(
                  color: WidgetStateProperty.all(bgColor),
                  cells: [
                    DataCell(Text(
                      '#${index + 1}',
                      style: TextStyle(
                        color: textColor,
                        fontWeight:
                            isCurrentUser ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 14,
                      ),
                    )),
                    DataCell(Text(
                      isCurrentUser ? currentName : (player.username ?? 'N/A'),
                      style: TextStyle(
                        color: textColor,
                        fontWeight:
                            isCurrentUser ? FontWeight.w600 : FontWeight.w400,
                        fontSize: 14,
                      ),
                    )),
                    DataCell(Text(
                      player.score?.toString() ?? '0',
                      style: TextStyle(
                        color: textColor,
                        fontWeight:
                            isCurrentUser ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 14,
                      ),
                    )),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
        Container(height: 150, color: Colors.transparent),
      ],
    );
  }
}
