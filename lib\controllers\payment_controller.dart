import 'dart:async';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/notification_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'dart:io' show Platform;
import 'analytics_controller.dart';
import 'auth_controller.dart';
import 'index_controller.dart';

class PurchaseApi extends GetxController {
  static String link = '';
  static String userID = '';
  static StreamSubscription<CustomerInfo>? _customerInfoListener;
  static final RxBool isInitialized = false.obs;
  static final RxBool isPurchasing = false.obs;
  static final Rx<CustomerInfo?> currentCustomerInfo = Rx<CustomerInfo?>(null);

  // Subscription states
  static final RxBool hasActiveSubscription = false.obs;
  static final RxBool isInGracePeriod = false.obs;
  static final RxString subscriptionExpiryDate = ''.obs;
  static final RxString subscriptionType = ''.obs; // 'monthly' or 'yearly'

  static Future<bool> init() async {
    try {
      if (isInitialized.value) {
        print('RevenueCat already initialized');
        return true;
      }

      String googleApiKey = dotenv.get("ANDROID_REVCAT_API_KEY", fallback: "");
      String appleApiKey = dotenv.get("IOS_REVCAT_API_KEY", fallback: "");

      if (googleApiKey.isEmpty && Platform.isAndroid) {
        throw Exception('Android RevenueCat API key is missing');
      }
      if (appleApiKey.isEmpty && Platform.isIOS) {
        throw Exception('iOS RevenueCat API key is missing');
      }

      AuthController authController = Get.find();
      if (authController.user == null) {
        throw Exception('User not authenticated');
      }

      await Purchases.setLogLevel(LogLevel.debug);

      PurchasesConfiguration configuration;
      if (Platform.isAndroid) {
        configuration = PurchasesConfiguration(googleApiKey)
          ..appUserID = authController.user!.uid;
      } else if (Platform.isIOS) {
        configuration = PurchasesConfiguration(appleApiKey)
          ..appUserID = authController.user!.uid;
      } else {
        throw Exception('Unsupported platform');
      }

      await Purchases.configure(configuration);
      isInitialized.value = true;

      // Setup listener after initialization
      setupCustomerInfoListener();

      // Check initial subscription status
      await checkSubscriptionStatus();

      return true;
    } catch (e) {
      print('Error initializing RevenueCat: $e');
      isInitialized.value = false;
      return false;
    }
  }

  static void setupCustomerInfoListener() {
    // Cancel existing listener if any
    _customerInfoListener?.cancel();

    Purchases.addCustomerInfoUpdateListener((customerInfo) {
      print('Customer info updated');
      currentCustomerInfo.value = customerInfo;
      updateSubscriptionStatus(customerInfo);
    });
  }

  static void updateSubscriptionStatus(CustomerInfo customerInfo) {
    ProfileController profileController = Get.find();

    // Check for active subscription
    final hasActive = customerInfo.entitlements.active.containsKey("Premium");
    hasActiveSubscription.value = hasActive;

    if (hasActive) {
      final entitlement = customerInfo.entitlements.active["Premium"]!;

      // Check if in grace period
      isInGracePeriod.value = entitlement.isActive &&
          entitlement.expirationDate != null &&
          DateTime.now().isAfter(DateTime.parse(entitlement.expirationDate!));


      // Store expiry date
      subscriptionExpiryDate.value = entitlement.expirationDate ?? '';

      // Determine subscription type from product identifier
      if (entitlement.productIdentifier.contains('monthly')) {
        subscriptionType.value = 'monthly';
      } else if (entitlement.productIdentifier.contains('yearly')) {
        subscriptionType.value = 'yearly';
      }

      profileController.isPremiumUser.value = true;
      profileController.updateUserSubscription();
    } else {
      // No active subscription
      profileController.isPremiumUser.value = false;
      isInGracePeriod.value = false;
      subscriptionExpiryDate.value = '';
      subscriptionType.value = '';

      NotificationService().cancelNotification();
      profileController.updateUserSubscription();
    }

    profileController.update();
  }

  static Future<bool> checkSubscriptionStatus() async {
    try {
      if (!isInitialized.value) {
        print('RevenueCat not initialized');
        return false;
      }

      final customerInfo = await Purchases.getCustomerInfo();
      currentCustomerInfo.value = customerInfo;
      updateSubscriptionStatus(customerInfo);

      // Get management URL if available
      if (customerInfo.managementURL != null) {
        link = customerInfo.managementURL!;
        userID = customerInfo.originalAppUserId;
      }

      return hasActiveSubscription.value;
    } catch (e) {
      print('Error checking subscription status: $e');
      return false;
    }
  }

  static Future<List<Offering>> fetchOffers() async {
    try {
      if (!isInitialized.value) {
        await init();
      }

      final offerings = await Purchases.getOfferings();

      final current = offerings.current;

      if (current == null) {
        print('No current offering available');
        return [];
      }

      // Log available packages for debugging
      for (var package in current.availablePackages) {
        print(
            'Package: ${package.identifier}, Price: ${package.storeProduct.price}');
      }

      return [current];
    } catch (e) {
      print('Error fetching offers: $e');
      return [];
    }
  }

  static Future<PurchaseResult> purchasePackage(Package package,
      {bool isFreeTimeSelected = false, bool isUserInitiated = true}) async {
    if (isPurchasing.value) {
      return PurchaseResult(
          success: false,
          error: 'Purchase already in progress',
          errorCode: 'PURCHASE_IN_PROGRESS');
    }

    ProfileController profileController = Get.find();
    final IndexController indexController = Get.find();
    final AnalticsController analticsController = AnalticsController();

    try {
      isPurchasing.value = true;

      if (!isInitialized.value) {
        throw PlatformException(
            code: 'NOT_INITIALIZED', message: 'RevenueCat not initialized');
      }

      // Perform the purchase
      final customerInfo = await Purchases.purchasePackage(package);
      print('revenuecatrevenuecat: ${customerInfo.entitlements.active}');

      // Check if purchase was successful
      if (customerInfo.entitlements.active.containsKey("Premium")) {
        // Analytics
        if (package.storeProduct.defaultOption?.billingPeriod?.iso8601 ==
            'P1M') {
          analticsController.subPlanMonthlyAnalyticsUpdate();
        } else {
          analticsController.subPlanYearlyAnalyticsUpdate();
        }

        // Update user state
        profileController.userr.update((user) {
          user?.isPremiumUser = true;
        });
        profileController.updateUserSubscription();

        // Navigate to home
        indexController.selectedIndex.value = 0;

        // Update subscription status
        updateSubscriptionStatus(customerInfo);

        return PurchaseResult(success: true, customerInfo: customerInfo);
      } else {
        throw PlatformException(
            code: 'PURCHASE_FAILED',
            message: 'Purchase completed but entitlement not granted');
      }
    } on PlatformException catch (e) {
      return _handlePurchaseError(e, isUserInitiated);
    } catch (e) {
      return PurchaseResult(
          success: false,
          error: 'An unexpected error occurred: $e',
          errorCode: 'UNKNOWN_ERROR');
    } finally {
      isPurchasing.value = false;
    }
  }

  static PurchaseResult _handlePurchaseError(PlatformException e,
      [bool isUserInitiated = true]) {
    String userFriendlyMessage;

    switch (e.code) {
      case 'PurchaseCancelledError':
      case '1': // Numeric code for PurchaseCancelledError
        // User cancelled - no need to show error
        return PurchaseResult(
            success: false, error: null, errorCode: e.code, cancelled: true);

      case 'StoreProblemError':
        userFriendlyMessage =
            'Dogodila se greška sa prodavnicom. Molimo pokušajte ponovo kasnije.';
        break;

      case 'PurchaseNotAllowedError':
        userFriendlyMessage = 'Kupovina nije dozvoljena na ovom uređaju.';
        break;

      case 'PurchaseInvalidError':
        userFriendlyMessage =
            'Neispravna kupovina. Molimo kontaktirajte podršku.';
        break;

      case 'ProductNotAvailableForPurchaseError':
        userFriendlyMessage =
            'Ovaj proizvod trenutno nije dostupan za kupovinu.';
        break;

      case 'ProductAlreadyPurchasedError':
      case '6':
        // Don't show error - instead refresh subscription status and treat as success
        checkSubscriptionStatus();
        return PurchaseResult(success: true, error: null, errorCode: e.code);

      case 'ReceiptAlreadyInUseError':
        userFriendlyMessage = 'Ova kupovina je već povezana sa drugim nalogom.';
        break;

      case 'MissingReceiptFileError':
        userFriendlyMessage =
            'Nedostaje potvrda o kupovini. Molimo pokušajte da vratite kupovinu.';
        break;

      case 'NetworkError':
        userFriendlyMessage =
            'Greška mreže. Proverite internetsku vezu i pokušajte ponovo.';
        break;

      case 'PaymentPendingError':
        userFriendlyMessage =
            'Vaša uplata je na čekanju. Bićete obavešteni kada bude završena.';
        break;

      case 'UnknownBackendError':
        userFriendlyMessage =
            'Greška servera. Molimo pokušajte ponovo kasnije.';
        break;

      case 'NOT_INITIALIZED':
        userFriendlyMessage =
            'Sistem za plaćanje nije spreman. Molimo pokušajte ponovo.';
        break;

      default:
        print(
            'revenuecatrevenuecat Unhandled purchase error code: ${e.code}, message: ${e.message}');
        userFriendlyMessage = 'Nešto je pošlo po zlu. Molimo pokušajte ponovo.';
    }

    // Only show error snackbar for user-initiated purchase attempts
    if (isUserInitiated &&
        e.code != 'PurchaseCancelledError' &&
        e.code != '1') {
      print('revenuecat Purchase error: $e');
      getErrorSnackBar(userFriendlyMessage);
    } else {
      // Just log background/initialization errors, don't show to user
      print('revenuecat Background error (not shown to user): $e');
    }

    return PurchaseResult(
        success: false, error: userFriendlyMessage, errorCode: e.code);
  }

  static Future<bool> restorePurchases() async {
    try {
      if (!isInitialized.value) {
        await init();
      }

      final customerInfo = await Purchases.restorePurchases();
      updateSubscriptionStatus(customerInfo);

      if (customerInfo.entitlements.active.containsKey("Premium")) {
        getSuccessSnackBar('Pretplata je uspešno vraćena!');
        return true;
      } else {
        getErrorSnackBar('Nema pronađenih pretplata za vraćanje.');
        return false;
      }
    } catch (e) {
      print('Error restoring purchases: $e');
      getErrorSnackBar(
          'Greška pri vraćanju kupovina. Molimo pokušajte ponovo.');
      return false;
    }
  }


  static Future<String?> getManagementURL() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      return customerInfo.managementURL;
    } catch (e) {
      print('Error getting management URL: $e');
      return null;
    }
  }

  static Future<bool> cancelSubscription() async {
    try {
      final url = await getManagementURL();
      if (url != null) {
        // Open management URL where user can cancel
        // You'll need to implement URL launcher
        // await launchUrl(Uri.parse(url));
        return true;
      }
      return false;
    } catch (e) {
      print('Error cancelling subscription: $e');
      return false;
    }
  }

  static void disposeListeners() {
    _customerInfoListener?.cancel();
    _customerInfoListener = null;
  }

  // Method to check and update premium status on app start
  static Future<void> premiumCheckerUpdate() async {
    try {
      if (!isInitialized.value) {
        await init();
      }
      await checkSubscriptionStatus();
    } catch (e) {
      print('Error in premiumCheckerUpdate: $e');
    }
  }

  // Helper method to get subscription details
  static Map<String, dynamic> getSubscriptionDetails() {
    return {
      'hasActiveSubscription': hasActiveSubscription.value,
      'isInGracePeriod': isInGracePeriod.value,
      'subscriptionExpiryDate': subscriptionExpiryDate.value,
      'subscriptionType': subscriptionType.value,
    };
  }
}

// Result class for better error handling
class PurchaseResult {
  final bool success;
  final String? error;
  final String? errorCode;
  final CustomerInfo? customerInfo;
  final bool cancelled;

  PurchaseResult({
    required this.success,
    this.error,
    this.errorCode,
    this.customerInfo,
    this.cancelled = false,
  });
}
