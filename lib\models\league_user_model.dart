import 'package:cloud_firestore/cloud_firestore.dart';

class LeaguePlayerModel {
  String? username;
  String? league;
  String? groupId; // Added groupId field for group-based leaderboards
  int? weeklyScore;
  String? playerId;
  Timestamp? lastUpdated;

  LeaguePlayerModel({
    this.username,
    this.league,
    this.groupId,
    this.weeklyScore,
    this.playerId,
    this.lastUpdated,
  });

  factory LeaguePlayerModel.fromJson(Map<String, dynamic> json) {
    return LeaguePlayerModel(
      username: json['username'],
      league: json['league'],
      groupId: json['groupId'],
      weeklyScore: json['weeklyScore'] ?? 0,
      playerId: json['playerId'],
      lastUpdated: json['lastUpdated'] as Timestamp?,
    );
  }

  Map<String, dynamic> toJson() => {
        'username': username,
        'league': league,
        'groupId': groupId,
        'weeklyScore': weeklyScore,
        'playerId': playerId,
        'lastUpdated': lastUpdated,
      };

  static LeaguePlayerModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return LeaguePlayerModel.fromJson(snapshot);
  }
}
